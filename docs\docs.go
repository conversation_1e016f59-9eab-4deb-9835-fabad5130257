// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admins": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get a list of all admin users with their basic information (ID, email, phone number, contact address, and full name)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "admins"
                ],
                "summary": "Get Admin Users",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.AdminForGet"
                            }
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new admin user with role \"Admin\"\n\nField Constraints:\n- fullName: Required field\n- email: Must be valid email format and unique across all users (required)\n- phoneNumber: Must be unique across all users (required)\n- password: Must be at least 6 characters long (required)\n- contactAddress: Optional field",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "admins"
                ],
                "summary": "Create Admin User",
                "parameters": [
                    {
                        "description": "Admin user details",
                        "name": "admin",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AdminForCreate"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.CreatedAdminResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/chapters": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get chapters for a subject_id",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chapters"
                ],
                "summary": "Get Chapters",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Subject ID",
                        "name": "subject_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Chapter"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new chapter\n\nField Constraints:\n- name: Chapter name must be unique (required)\n- displayName: Display name for the chapter (required)\n- subjectName: Must reference an existing subject (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chapters"
                ],
                "summary": "CreateChapters",
                "parameters": [
                    {
                        "description": "chapter details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ChapterForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/comments": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get comments for a video or study material. For students, returns only their comments. For admins, returns all comments.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "comments"
                ],
                "summary": "Get Comments",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Video ID",
                        "name": "video_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Study Material ID",
                        "name": "material_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CommentsResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Add a comment to a video or study material",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "comments"
                ],
                "summary": "Add Comment",
                "parameters": [
                    {
                        "description": "comment details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CommentForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Comment"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/content": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get videos and study materials organized by subjects with progress information. For students, also returns recently accessed content organized by subjects, with videos and PDFs sorted by last accessed timestamps.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "content"
                ],
                "summary": "Get Content",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Course ID (optional - if not provided, returns content for all courses)",
                        "name": "course_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.ContentResponseWithProgress"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/courses": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get courses for the logged in user. Returns all courses for non-student users (admin, etc.) and filtered courses for students (free courses + enrolled paid courses)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "courses"
                ],
                "summary": "Get Courses",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CoursesByCategory"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new course\n\nField Constraints:\n- courseType: Must be one of 'IIT-JEE', 'NEET' (required)\n- name: Must be unique across all courses (required)\n- isFree: Boolean flag indicating if course is free (defaults to false)\n- subjectNames: Array of existing subject names to associate with the course (optional)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "courses"
                ],
                "summary": "CreateCourse",
                "parameters": [
                    {
                        "description": "course details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CourseForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/courses/{course_id}/tests/{test_id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "associate an existing test with an existing course",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "courses"
                ],
                "summary": "Associate Test with Course",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Course ID",
                        "name": "course_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/courses/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get detailed information about a specific course including subjects, chapters, videos, and study materials",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "courses"
                ],
                "summary": "Get Course Details",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Course ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CourseDetails"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/enroll/{course_id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "enroll student in a course",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "EnrollInCourse",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "course ID to enroll in",
                        "name": "course_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Student"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/formula-cards": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get all formula cards organized by subject, chapter, and topic with counts",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "formula-cards"
                ],
                "summary": "Get Formula Cards",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.FormulaCardsBySubject"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create multiple formula cards for a topic\n\nField Constraints:\n- subjectName: Must reference an existing subject (required)\n- chapterName: Must reference an existing chapter within the subject (required)\n- topicName: Must reference an existing topic within the chapter (required)\n- formulaCards: Array must contain at least 1 formula card (required)\n- name: Required for each formula card\n- imageUrl: Required for each formula card",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "formula-cards"
                ],
                "summary": "CreateFormulaCards",
                "parameters": [
                    {
                        "description": "formula cards details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.FormulaCardsForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.SimpleEntityResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/institutions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get all institutions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "institutions"
                ],
                "summary": "Get Institutions",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Institution"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create a new institution\n\nField Constraints:\n- name: Institution name (required)\n- city_or_town: City or town where institution is located (required)\n- state: State where institution is located (required)\n- contact_name: Name of the contact person (required)\n- contact_number: Contact phone number (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "institutions"
                ],
                "summary": "Create Institution",
                "parameters": [
                    {
                        "description": "institution details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.InstitutionForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/institutions/{id}": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "update an existing institution\n\nField Constraints:\n- name: Institution name (required)\n- city_or_town: City or town where institution is located (required)\n- state: State where institution is located (required)\n- contact_name: Name of the contact person (required)\n- contact_number: Contact phone number (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "institutions"
                ],
                "summary": "Update Institution",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Institution ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "institution details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.InstitutionForUpdate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Institution"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "delete an institution (soft delete)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "institutions"
                ],
                "summary": "Delete Institution",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Institution ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/login": {
            "post": {
                "description": "login with email and password. Returns StudentLoginResponse for students and AdminLoginResponse for admins",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "login"
                ],
                "summary": "Login",
                "parameters": [
                    {
                        "description": "user_email and password",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Credentials"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Admin login response",
                        "schema": {
                            "$ref": "#/definitions/models.AdminLoginResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/orders": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new Razorpay order for payment processing\n\nField Constraints:\n- amount: Transaction amount in paise (required, minimum 1)\nThe API will generate a unique receipt ID and create a Razorpay order",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "payments"
                ],
                "summary": "Create Razorpay Order",
                "parameters": [
                    {
                        "description": "order details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.OrderForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.OrderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/previous-year-papers": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get all previous year papers organized by exam type, sorted by year (descending) then month (descending)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "previous-year-papers"
                ],
                "summary": "Get All Previous Year Papers Organized by Exam Type",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.PreviousYearPapersByExamType"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create multiple previous year papers in bulk\n\nField Constraints:\n- examType: Must be one of 'IIT-JEE', 'NEET' (required)\n- month: Must be between 1 and 12 (required)\n- year: Must be between 1900 and 2100 (required)\n- pdfUrl: Required for each paper",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "previous-year-papers"
                ],
                "summary": "CreatePreviousYearPapers",
                "parameters": [
                    {
                        "description": "previous year papers details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PreviousYearPapersForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.SimpleEntityResponse"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/questions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get questions with optional filters for subject, chapter, topic, and difficulty",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "Get Questions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Subject name (optional)",
                        "name": "subject",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Chapter name (optional)",
                        "name": "chapter",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Topic name (optional)",
                        "name": "topic",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Difficulty level (optional)",
                        "name": "difficulty",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Question"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new question\n\nField Constraints:\n- text: Question text is required\n- topicName: Must reference an existing topic (required)\n- difficultyName: Must reference an existing difficulty level (required)\n- questionType: Type of question (e.g., 'MCQ', 'text')\n- correctAnswer: Only used for text questions, not for MCQ questions\n- options: Required for MCQ questions, should contain option details",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "CreateQuestion",
                "parameters": [
                    {
                        "description": "question details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.QuestionForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/responses": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Add a response to an existing comment",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "comments"
                ],
                "summary": "Add Response",
                "parameters": [
                    {
                        "description": "response details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ResponseForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/section-types": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get all section types with their associated subjects",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "Get Section Types",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.SectionType"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new section type\n\nField Constraints:\n- name: Section type name must be unique (required)\n- subjectName: Must reference an existing subject (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "CreateSectionType",
                "parameters": [
                    {
                        "description": "section type details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SectionTypeForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/students": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get list of all students with optional filters\n\nOptional Filters:\n- stream: Filter by stream ('IIT-JEE' or 'NEET')\n- class: Filter by class ('9th', '10th', '11th', '12th', 'dropper')\n- name: Prefix match on student's full name\n- email: Prefix match on student's email\n- institution: Exact match on student's institution\n- phone_number: Prefix match on student's phone number",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "Get Students",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Stream filter (IIT-JEE or NEET)",
                        "name": "stream",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Class filter (9th, 10th, 11th, 12th, dropper)",
                        "name": "class",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Name prefix filter",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Email prefix filter",
                        "name": "email",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Phone number prefix filter",
                        "name": "phone_number",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Institution exact match filter",
                        "name": "institution",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.StudentForGet"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "description": "create new student with enhanced information including institute, class, stream, city and state\n\nField Constraints:\n- class: Must be one of '9th', '10th', '11th', '12th', 'dropper' (optional)\n- stream: Must be one of 'IIT-JEE', 'NEET' (optional)\n- email: Must be unique across all users\n- phoneNumber: Must be unique across all users\n- password: Must be at least 6 characters long (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "CreateStudent",
                "parameters": [
                    {
                        "description": "student details with enhanced fields",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.StudentForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CreatedStudentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/students/send-verification-code": {
            "post": {
                "description": "send verification code to student via SMS or email\n\nField Constraints:\n- phone_number: Must be a valid phone number",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "SendVerificationCode",
                "parameters": [
                    {
                        "description": "verification code request details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SendVerificationCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SendVerificationCodeResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/students/verify-code": {
            "post": {
                "description": "verify the code entered by student\n\nField Constraints:\n- phone_number: Must be a valid phone number\n- code: Must be the verification code received via SMS",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "VerifyCode",
                "parameters": [
                    {
                        "description": "verification code details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VerifyCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.VerifyCodeResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/study-material-progress": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update or create study material read progress for the authenticated student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "study-material-progress"
                ],
                "summary": "Update Study Material Progress",
                "parameters": [
                    {
                        "description": "Study material progress data",
                        "name": "progress",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.StudyMaterialProgressForUpdate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/studymaterials": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "add a new material",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "library"
                ],
                "summary": "AddStudyMaterial",
                "parameters": [
                    {
                        "description": "material details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MaterialForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/subjects": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get subjects for logged in student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "library"
                ],
                "summary": "Get Subjects",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Subject"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new subject\n\nField Constraints:\n- name: Subject name must be unique (required)\n- displayName: Display name for the subject (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "subjects"
                ],
                "summary": "CreateSubject",
                "parameters": [
                    {
                        "description": "subject details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SubjectForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/test-responses": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Record student responses for all questions in a test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "test-responses"
                ],
                "summary": "RecordTestResponses",
                "parameters": [
                    {
                        "description": "test responses",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TestResponsesForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.TestResponsesResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/test-responses/rankings/{test_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get rankings for all students in a specific test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "test-responses"
                ],
                "summary": "GetTestRankings",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Limit number of results (default: 100)",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Offset for pagination (default: 0)",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.TestRankingResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/test-responses/{test_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get student responses for a specific test with total score and recording timestamp",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "test-responses"
                ],
                "summary": "GetStudentTestResponses",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.StudentTestResponsesResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/test-types": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get all test types with their associated section types",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "Get Test Types",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.TestType"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new test type\n\nField Constraints:\n- name: Test type name must be unique (required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "CreateTestType",
                "parameters": [
                    {
                        "description": "test type details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TestTypeForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/tests": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get tests for the logged in user. Students see tests from enrolled courses and all ZSAT type tests. Admins see all tests.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "Get Tests",
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "Filter by active status (true/false)",
                        "name": "active",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.TestForGet"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new test of a given type\n\nField Constraints:\n- name: Test name is required\n- testTypeName: Must reference an existing test type (required)\n- fromTime: Start time for the test (required)\n- toTime: End time for the test (required)\n- sections: Array of sections for the test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "CreateTest",
                "parameters": [
                    {
                        "description": "test details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TestForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/tests/{test_id}/active": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "toggle active status of a test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "Toggle Test Active Status",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/tests/{test_id}/questions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get all questions for a specific test organized by sections",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "Get Test Questions",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.TestQuestionsResult"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "add questions to a test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "AddQuestionsToTest",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "question IDs and section name",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AddQuestionsToTestRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "remove questions from a test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "RemoveQuestionsFromTest",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "question IDs and section name",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.RemoveQuestionsFromTestRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/tests/{test_id}/results-disclosure": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Toggle the results disclosure status of a test (Admin only)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "ToggleTestResultsDisclosure",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/topics": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get topics for a chapter_name, or all topics if no chapter_name provided",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "Get Topics",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Chapter Name (optional)",
                        "name": "chapter_name",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.TopicSummary"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new topic for questions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "CreateTopic",
                "parameters": [
                    {
                        "description": "topic details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TopicForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/transactions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get transaction history for the logged-in student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "transactions"
                ],
                "summary": "Get Transactions",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.TransactionHistory"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new transaction for course purchase\n\nField Constraints:\n- courseIds: Array of course IDs to purchase (required, must contain at least 1 ID)\n- paymentMethod: Payment method identifier (required)\nTransaction status will be set to 'PENDING' initially",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "transactions"
                ],
                "summary": "Create Transaction",
                "parameters": [
                    {
                        "description": "transaction details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TransactionForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Transaction"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/transactions/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get specific transaction details by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "transactions"
                ],
                "summary": "Get Transaction by ID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "transaction ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Transaction"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/transactions/{id}/status": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update transaction status (admin/webhook endpoint)\n\nField Constraints:\n- status: Must be one of 'PENDING', 'COMPLETED', 'FAILED', 'CANCELLED' (required)\n- paymentReference: External payment gateway reference (optional)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "transactions"
                ],
                "summary": "Update Transaction Status",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "transaction ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "status update details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TransactionStatusUpdate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/users/password": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "update password for logged in user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "users"
                ],
                "summary": "UpdatePassword",
                "parameters": [
                    {
                        "description": "new password",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdatePassword"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/video-progress": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update or create video watch progress for the authenticated student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "video-progress"
                ],
                "summary": "Update Video Progress",
                "parameters": [
                    {
                        "description": "Video progress data",
                        "name": "progress",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VideoProgressForUpdate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/videos": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "add a new video",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "library"
                ],
                "summary": "AddVideo",
                "parameters": [
                    {
                        "description": "video details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VideoForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SimpleEntityResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/webhook/razorpay": {
            "post": {
                "description": "Handle webhook notifications from Razorpay for payment events",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "payments"
                ],
                "summary": "Handle Razorpay Webhook",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "gorm.DeletedAt": {
            "type": "object",
            "properties": {
                "time": {
                    "type": "string"
                },
                "valid": {
                    "description": "Valid is true if Time is not NULL",
                    "type": "boolean"
                }
            }
        },
        "http.HTTPError": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 400
                },
                "message": {
                    "type": "string",
                    "example": "status bad request"
                }
            }
        },
        "models.AddQuestionsToTestRequest": {
            "type": "object",
            "required": [
                "question_ids",
                "section_name"
            ],
            "properties": {
                "question_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "section_name": {
                    "type": "string"
                }
            }
        },
        "models.AdminForCreate": {
            "type": "object",
            "required": [
                "email",
                "full_name",
                "password",
                "phone_number"
            ],
            "properties": {
                "contact_address": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "phone_number": {
                    "type": "string"
                }
            }
        },
        "models.AdminForGet": {
            "type": "object",
            "properties": {
                "contact_address": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "phone_number": {
                    "type": "string"
                }
            }
        },
        "models.AdminLoginResponse": {
            "type": "object",
            "properties": {
                "role": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.UserForGet"
                }
            }
        },
        "models.Chapter": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "studyMaterials": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudyMaterial"
                    }
                },
                "subject": {
                    "$ref": "#/definitions/models.Subject"
                },
                "subjectID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                },
                "videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Video"
                    }
                }
            }
        },
        "models.ChapterDetails": {
            "type": "object",
            "properties": {
                "display_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "study_materials": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudyMaterialForGet"
                    }
                },
                "videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VideoForGet"
                    }
                }
            }
        },
        "models.ChapterForCreate": {
            "type": "object",
            "properties": {
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "subjectName": {
                    "type": "string"
                }
            }
        },
        "models.Comment": {
            "type": "object",
            "properties": {
                "commentText": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "material": {
                    "$ref": "#/definitions/models.StudyMaterial"
                },
                "materialID": {
                    "description": "Optional: or to a study material",
                    "type": "integer"
                },
                "responses": {
                    "description": "Replies to this comment",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Response"
                    }
                },
                "updatedAt": {
                    "type": "string"
                },
                "user": {
                    "description": "Assumes a User model exists",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.User"
                        }
                    ]
                },
                "userID": {
                    "description": "FK to users",
                    "type": "integer"
                },
                "video": {
                    "$ref": "#/definitions/models.Video"
                },
                "videoID": {
                    "description": "Optional: comments can belong to a video",
                    "type": "integer"
                }
            }
        },
        "models.CommentForCreate": {
            "type": "object",
            "required": [
                "comment_text"
            ],
            "properties": {
                "comment_text": {
                    "type": "string"
                },
                "material_id": {
                    "type": "integer"
                },
                "video_id": {
                    "type": "integer"
                }
            }
        },
        "models.CommentWithResponses": {
            "type": "object",
            "properties": {
                "comment_text": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "material_id": {
                    "type": "integer"
                },
                "responses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ResponseOutput"
                    }
                },
                "user_id": {
                    "type": "integer"
                },
                "user_name": {
                    "type": "string"
                },
                "video_id": {
                    "type": "integer"
                }
            }
        },
        "models.CommentsResponse": {
            "type": "object",
            "properties": {
                "comments": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CommentWithResponses"
                    }
                },
                "material_id": {
                    "type": "integer"
                },
                "video_id": {
                    "type": "integer"
                }
            }
        },
        "models.ContentResponseWithProgress": {
            "type": "object",
            "properties": {
                "recently_accessed_content": {
                    "description": "Recently accessed content organized by subjects (only for students)",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/models.RecentlyAccessedContent"
                    }
                },
                "subjects": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/models.SubjectContentWithProgress"
                    }
                },
                "summary": {
                    "$ref": "#/definitions/models.ContentSummary"
                }
            }
        },
        "models.ContentSummary": {
            "type": "object",
            "properties": {
                "total_pdfs": {
                    "type": "integer"
                },
                "total_subjects": {
                    "type": "integer"
                },
                "total_videos": {
                    "type": "integer"
                }
            }
        },
        "models.Course": {
            "type": "object",
            "properties": {
                "courseType": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number",
                    "format": "float32"
                },
                "durationInDays": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "isFree": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "subjects": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Subject"
                    }
                },
                "tests": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Test"
                    }
                },
                "tileImageUrl": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.CourseDetails": {
            "type": "object",
            "properties": {
                "course_type": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number"
                },
                "duration_in_days": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "is_free": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "subjects": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SubjectDetails"
                    }
                },
                "tile_image_url": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "models.CourseForCreate": {
            "type": "object",
            "properties": {
                "courseType": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number",
                    "format": "float32"
                },
                "durationInDays": {
                    "type": "integer"
                },
                "isFree": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "subjectNames": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "tileImageUrl": {
                    "type": "string"
                }
            }
        },
        "models.CourseWithPurchased": {
            "type": "object",
            "properties": {
                "courseType": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number",
                    "format": "float32"
                },
                "durationInDays": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "isFree": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "purchased": {
                    "type": "boolean"
                },
                "tileImageUrl": {
                    "type": "string"
                }
            }
        },
        "models.CoursesByCategory": {
            "type": "object",
            "properties": {
                "free_courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CoursesByType"
                    }
                },
                "paid_courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CoursesByType"
                    }
                }
            }
        },
        "models.CoursesByType": {
            "type": "object",
            "properties": {
                "course_type": {
                    "type": "string"
                },
                "courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CourseWithPurchased"
                    }
                }
            }
        },
        "models.CreatedAdminResponse": {
            "type": "object",
            "properties": {
                "admin": {
                    "$ref": "#/definitions/models.SimpleEntityResponse"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "models.CreatedStudentResponse": {
            "type": "object",
            "properties": {
                "student": {
                    "$ref": "#/definitions/models.StudentForGet"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "models.Credentials": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string"
                },
                "user_email": {
                    "type": "string"
                }
            }
        },
        "models.Difficulty": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Question"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.FormulaCard": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "imageUrl": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "topic": {
                    "$ref": "#/definitions/models.Topic"
                },
                "topicID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.FormulaCardForCreate": {
            "type": "object",
            "required": [
                "image_url",
                "name"
            ],
            "properties": {
                "image_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.FormulaCardSummary": {
            "type": "object",
            "properties": {
                "chapter_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "image_url": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "subject_name": {
                    "type": "string"
                },
                "topic_name": {
                    "type": "string"
                }
            }
        },
        "models.FormulaCardsByChapter": {
            "type": "object",
            "properties": {
                "chapter_name": {
                    "type": "string"
                },
                "count": {
                    "type": "integer"
                },
                "topics": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.FormulaCardsByTopic"
                    }
                }
            }
        },
        "models.FormulaCardsBySubject": {
            "type": "object",
            "properties": {
                "chapters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.FormulaCardsByChapter"
                    }
                },
                "count": {
                    "type": "integer"
                },
                "subject_name": {
                    "type": "string"
                }
            }
        },
        "models.FormulaCardsByTopic": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "formula_cards": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.FormulaCardSummary"
                    }
                },
                "topic_name": {
                    "type": "string"
                }
            }
        },
        "models.FormulaCardsForCreate": {
            "type": "object",
            "required": [
                "chapter_name",
                "formula_cards",
                "subject_name",
                "topic_name"
            ],
            "properties": {
                "chapter_name": {
                    "type": "string"
                },
                "formula_cards": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/models.FormulaCardForCreate"
                    }
                },
                "subject_name": {
                    "type": "string"
                },
                "topic_name": {
                    "type": "string"
                }
            }
        },
        "models.Institution": {
            "type": "object",
            "properties": {
                "city_or_town": {
                    "type": "string"
                },
                "contact_name": {
                    "type": "string"
                },
                "contact_number": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.InstitutionForCreate": {
            "type": "object",
            "required": [
                "city_or_town",
                "contact_name",
                "contact_number",
                "name",
                "state"
            ],
            "properties": {
                "city_or_town": {
                    "type": "string"
                },
                "contact_name": {
                    "type": "string"
                },
                "contact_number": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "models.InstitutionForUpdate": {
            "type": "object",
            "required": [
                "city_or_town",
                "contact_name",
                "contact_number",
                "name",
                "state"
            ],
            "properties": {
                "city_or_town": {
                    "type": "string"
                },
                "contact_name": {
                    "type": "string"
                },
                "contact_number": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                }
            }
        },
        "models.MaterialForCreate": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "tileImageUrl": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.Option": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "isCorrect": {
                    "description": "Correctness flag",
                    "type": "boolean"
                },
                "optionImageURL": {
                    "description": "Optional image URL",
                    "type": "string"
                },
                "optionText": {
                    "description": "Option content",
                    "type": "string"
                },
                "question": {
                    "description": "GORM association",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Question"
                        }
                    ]
                },
                "questionID": {
                    "description": "Foreign key to Questions",
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.OptionForCreate": {
            "type": "object",
            "required": [
                "option_text"
            ],
            "properties": {
                "is_correct": {
                    "type": "boolean"
                },
                "option_image_url": {
                    "type": "string"
                },
                "option_text": {
                    "type": "string"
                }
            }
        },
        "models.OrderForCreate": {
            "type": "object",
            "required": [
                "amount"
            ],
            "properties": {
                "amount": {
                    "description": "amount in paise (e.g., ₹10 = 1000)",
                    "type": "integer",
                    "minimum": 1
                }
            }
        },
        "models.OrderResponse": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "integer"
                },
                "order_id": {
                    "type": "string"
                },
                "receipt": {
                    "type": "string"
                }
            }
        },
        "models.PreviousYearPaper": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "examType": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "month": {
                    "type": "integer"
                },
                "pdfUrl": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "year": {
                    "type": "integer"
                }
            }
        },
        "models.PreviousYearPaperForCreate": {
            "type": "object",
            "required": [
                "exam_type",
                "month",
                "pdf_url",
                "year"
            ],
            "properties": {
                "exam_type": {
                    "type": "string",
                    "enum": [
                        "IIT-JEE",
                        "NEET"
                    ]
                },
                "month": {
                    "type": "integer",
                    "maximum": 12,
                    "minimum": 1
                },
                "pdf_url": {
                    "type": "string"
                },
                "year": {
                    "type": "integer",
                    "maximum": 2100,
                    "minimum": 1900
                }
            }
        },
        "models.PreviousYearPapersByExamType": {
            "type": "object",
            "properties": {
                "exam_type": {
                    "type": "string"
                },
                "papers": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.PreviousYearPaper"
                    }
                }
            }
        },
        "models.PreviousYearPapersForCreate": {
            "type": "object",
            "required": [
                "papers"
            ],
            "properties": {
                "papers": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/models.PreviousYearPaperForCreate"
                    }
                }
            }
        },
        "models.Question": {
            "type": "object",
            "properties": {
                "correctAnswer": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "difficulty": {
                    "$ref": "#/definitions/models.Difficulty"
                },
                "difficultyID": {
                    "type": "integer"
                },
                "fileUrl": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "imageUrl": {
                    "type": "string"
                },
                "options": {
                    "description": "One-to-many relationship with options",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Option"
                    }
                },
                "questionType": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                },
                "topic": {
                    "$ref": "#/definitions/models.Topic"
                },
                "topicID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.QuestionForCreate": {
            "type": "object",
            "required": [
                "difficulty_name",
                "question_type",
                "text",
                "topic_name"
            ],
            "properties": {
                "correct_answer": {
                    "type": "string"
                },
                "difficulty_name": {
                    "type": "string"
                },
                "file_url": {
                    "type": "string"
                },
                "image_url": {
                    "type": "string"
                },
                "options": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OptionForCreate"
                    }
                },
                "question_type": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                },
                "topic_name": {
                    "type": "string"
                }
            }
        },
        "models.RecentlyAccessedContent": {
            "type": "object",
            "properties": {
                "display_name": {
                    "type": "string"
                },
                "pdfs": {
                    "description": "Recently read PDFs for this subject",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudyMaterialForGet"
                    }
                },
                "subject_id": {
                    "type": "integer"
                },
                "videos": {
                    "description": "Recently watched videos for this subject",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VideoForGet"
                    }
                }
            }
        },
        "models.RemoveQuestionsFromTestRequest": {
            "type": "object",
            "required": [
                "question_ids",
                "section_name"
            ],
            "properties": {
                "question_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "section_name": {
                    "type": "string"
                }
            }
        },
        "models.Response": {
            "type": "object",
            "properties": {
                "comment": {
                    "$ref": "#/definitions/models.Comment"
                },
                "commentID": {
                    "description": "FK to Comment",
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "responseText": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                },
                "userID": {
                    "description": "FK to User",
                    "type": "integer"
                }
            }
        },
        "models.ResponseForCreate": {
            "type": "object",
            "required": [
                "comment_id",
                "response_text"
            ],
            "properties": {
                "comment_id": {
                    "type": "integer"
                },
                "response_text": {
                    "type": "string"
                }
            }
        },
        "models.ResponseOutput": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "response_text": {
                    "type": "string"
                },
                "user_id": {
                    "type": "integer"
                },
                "user_name": {
                    "type": "string"
                }
            }
        },
        "models.Section": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Question"
                    }
                },
                "sectionType": {
                    "$ref": "#/definitions/models.SectionType"
                },
                "sectionTypeID": {
                    "type": "integer"
                },
                "test": {
                    "$ref": "#/definitions/models.Test"
                },
                "testID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SectionQuestionInfo": {
            "type": "object",
            "properties": {
                "expected_question_count": {
                    "type": "integer"
                },
                "question_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "models.SectionType": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "negativeMarks": {
                    "type": "number"
                },
                "positiveMarks": {
                    "type": "number"
                },
                "questionCount": {
                    "type": "integer"
                },
                "subject": {
                    "$ref": "#/definitions/models.Subject"
                },
                "subjectID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SectionTypeForCreate": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "negativeMarks": {
                    "type": "number",
                    "format": "float64"
                },
                "positiveMarks": {
                    "type": "number",
                    "format": "float64"
                },
                "questionCount": {
                    "type": "integer"
                },
                "subjectName": {
                    "type": "string"
                }
            }
        },
        "models.SendVerificationCodeRequest": {
            "type": "object",
            "required": [
                "phone_number"
            ],
            "properties": {
                "phone_number": {
                    "type": "string"
                }
            }
        },
        "models.SendVerificationCodeResponse": {
            "type": "object",
            "properties": {
                "code_sent": {
                    "type": "boolean"
                },
                "expires_at": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                }
            }
        },
        "models.SimpleEntityResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.Student": {
            "type": "object",
            "properties": {
                "city_or_town": {
                    "type": "string"
                },
                "class": {
                    "type": "string"
                },
                "courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Course"
                    }
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "institute": {
                    "type": "string"
                },
                "parent_email": {
                    "type": "string"
                },
                "parent_phone": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "stream": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                },
                "userID": {
                    "type": "integer"
                }
            }
        },
        "models.StudentForCreate": {
            "type": "object",
            "required": [
                "password"
            ],
            "properties": {
                "city_or_town": {
                    "type": "string"
                },
                "class": {
                    "type": "string",
                    "enum": [
                        "9th",
                        "10th",
                        "11th",
                        "12th",
                        "dropper"
                    ]
                },
                "contactAddress": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "fullName": {
                    "type": "string"
                },
                "institute": {
                    "type": "string"
                },
                "parent_email": {
                    "type": "string"
                },
                "parent_phone": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "phoneNumber": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "stream": {
                    "type": "string",
                    "enum": [
                        "IIT-JEE",
                        "NEET"
                    ]
                }
            }
        },
        "models.StudentForGet": {
            "type": "object",
            "properties": {
                "city_or_town": {
                    "type": "string"
                },
                "class": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "institute": {
                    "type": "string"
                },
                "parent_email": {
                    "type": "string"
                },
                "parent_phone": {
                    "type": "string"
                },
                "phone_number": {
                    "type": "string"
                },
                "state": {
                    "type": "string"
                },
                "stream": {
                    "type": "string"
                }
            }
        },
        "models.StudentLoginResponse": {
            "type": "object",
            "properties": {
                "role": {
                    "type": "string"
                },
                "student": {
                    "$ref": "#/definitions/models.StudentForGet"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "models.StudentRankingInfo": {
            "type": "object",
            "properties": {
                "final_marks": {
                    "type": "integer"
                },
                "percentile": {
                    "type": "number"
                },
                "rank": {
                    "type": "integer"
                },
                "student_email": {
                    "type": "string"
                },
                "student_id": {
                    "type": "integer"
                },
                "student_name": {
                    "type": "string"
                },
                "total_negative_marks": {
                    "type": "integer"
                },
                "total_positive_marks": {
                    "type": "integer"
                }
            }
        },
        "models.StudentTestResponsesResult": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "responses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TestResponse"
                    }
                },
                "responses_recorded_at": {
                    "type": "string"
                },
                "student_id": {
                    "type": "integer"
                },
                "student_name": {
                    "type": "string"
                },
                "test_id": {
                    "type": "integer"
                },
                "test_name": {
                    "type": "string"
                },
                "total_score": {
                    "type": "integer"
                }
            }
        },
        "models.StudyMaterial": {
            "type": "object",
            "properties": {
                "chapterID": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "tileImageUrl": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.StudyMaterialForGet": {
            "type": "object",
            "properties": {
                "chapter_id": {
                    "type": "integer"
                },
                "display_name": {
                    "type": "string"
                },
                "last_read_at": {
                    "description": "When the student last read this material (nil if never read)",
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "tile_image_url": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.StudyMaterialProgressForUpdate": {
            "type": "object",
            "required": [
                "study_material_id"
            ],
            "properties": {
                "study_material_id": {
                    "type": "integer"
                }
            }
        },
        "models.Subject": {
            "type": "object",
            "properties": {
                "chapters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Chapter"
                    }
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SubjectContentWithProgress": {
            "type": "object",
            "properties": {
                "display_name": {
                    "type": "string"
                },
                "pdf_count": {
                    "type": "integer"
                },
                "pdfs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudyMaterialForGet"
                    }
                },
                "subject_id": {
                    "type": "integer"
                },
                "video_count": {
                    "type": "integer"
                },
                "videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.VideoForGet"
                    }
                }
            }
        },
        "models.SubjectDetails": {
            "type": "object",
            "properties": {
                "chapters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ChapterDetails"
                    }
                },
                "display_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.SubjectForCreate": {
            "type": "object",
            "properties": {
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.SubjectScoreInfo": {
            "type": "object",
            "properties": {
                "average_score": {
                    "type": "number"
                },
                "topic_scores": {
                    "description": "topic_name -\u003e average_score",
                    "type": "object",
                    "additionalProperties": {
                        "type": "number",
                        "format": "float64"
                    }
                }
            }
        },
        "models.Test": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "fromTime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "resultsDisclosed": {
                    "type": "boolean"
                },
                "sections": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Section"
                    }
                },
                "testType": {
                    "$ref": "#/definitions/models.TestType"
                },
                "testTypeID": {
                    "type": "integer"
                },
                "toTime": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TestForCreate": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "fromTime": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "testTypeName": {
                    "type": "string"
                },
                "toTime": {
                    "type": "string"
                }
            }
        },
        "models.TestForGet": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "description": {
                    "type": "string"
                },
                "fromTime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "results_disclosed": {
                    "type": "boolean"
                },
                "sectionNames": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "section_questions": {
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/models.SectionQuestionInfo"
                    }
                },
                "testType": {
                    "type": "string"
                },
                "toTime": {
                    "type": "string"
                }
            }
        },
        "models.TestQuestionInfo": {
            "type": "object",
            "properties": {
                "difficulty_name": {
                    "type": "string"
                },
                "file_url": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "image_url": {
                    "type": "string"
                },
                "options": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Option"
                    }
                },
                "question_type": {
                    "type": "string"
                },
                "subject_name": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                },
                "topic_name": {
                    "type": "string"
                }
            }
        },
        "models.TestQuestionsResult": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "sections": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TestSectionInfo"
                    }
                },
                "test_id": {
                    "type": "integer"
                },
                "test_name": {
                    "type": "string"
                },
                "test_type": {
                    "type": "string"
                }
            }
        },
        "models.TestRankingResult": {
            "type": "object",
            "properties": {
                "average_marks": {
                    "type": "number"
                },
                "highest_marks": {
                    "type": "integer"
                },
                "lowest_marks": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "student_rankings": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudentRankingInfo"
                    }
                },
                "subject_scores": {
                    "description": "subject_name -\u003e SubjectScoreInfo",
                    "type": "object",
                    "additionalProperties": {
                        "$ref": "#/definitions/models.SubjectScoreInfo"
                    }
                },
                "test_id": {
                    "type": "integer"
                },
                "test_name": {
                    "type": "string"
                },
                "total_students": {
                    "type": "integer"
                }
            }
        },
        "models.TestResponse": {
            "type": "object",
            "properties": {
                "calculatedScore": {
                    "description": "Nullable, can be set after evaluation",
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "isCorrect": {
                    "description": "Automatically evaluated",
                    "type": "boolean"
                },
                "question": {
                    "$ref": "#/definitions/models.Question"
                },
                "questionID": {
                    "description": "FK to questions",
                    "type": "integer"
                },
                "responseText": {
                    "description": "Nullable for text answers",
                    "type": "string"
                },
                "selectedOptionIDs": {
                    "description": "PostgreSQL array type (use pgx or pq)",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "student": {
                    "description": "Assumes you have a Student model",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.Student"
                        }
                    ]
                },
                "studentID": {
                    "description": "FK to students",
                    "type": "integer"
                },
                "test": {
                    "$ref": "#/definitions/models.Test"
                },
                "testID": {
                    "description": "FK to tests",
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TestResponseForCreate": {
            "type": "object",
            "required": [
                "question_id"
            ],
            "properties": {
                "question_id": {
                    "type": "integer"
                },
                "response_text": {
                    "type": "string"
                },
                "selected_option_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "models.TestResponseResult": {
            "type": "object",
            "properties": {
                "calculated_score": {
                    "type": "integer"
                },
                "is_correct": {
                    "type": "boolean"
                },
                "message": {
                    "type": "string"
                },
                "question_id": {
                    "type": "integer"
                }
            }
        },
        "models.TestResponsesForCreate": {
            "type": "object",
            "required": [
                "responses",
                "test_id"
            ],
            "properties": {
                "responses": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "$ref": "#/definitions/models.TestResponseForCreate"
                    }
                },
                "test_id": {
                    "type": "integer"
                }
            }
        },
        "models.TestResponsesResult": {
            "type": "object",
            "properties": {
                "correct_answers": {
                    "type": "integer"
                },
                "message": {
                    "type": "string"
                },
                "response_results": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TestResponseResult"
                    }
                },
                "student_id": {
                    "type": "integer"
                },
                "test_id": {
                    "type": "integer"
                },
                "total_questions": {
                    "type": "integer"
                },
                "total_score": {
                    "type": "integer"
                }
            }
        },
        "models.TestSectionInfo": {
            "type": "object",
            "properties": {
                "display_name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TestQuestionInfo"
                    }
                },
                "section_id": {
                    "type": "integer"
                },
                "section_name": {
                    "type": "string"
                }
            }
        },
        "models.TestType": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "sectionTypes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SectionType"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TestTypeForCreate": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "sectionTypeNames": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.Topic": {
            "type": "object",
            "properties": {
                "chapter": {
                    "$ref": "#/definitions/models.Chapter"
                },
                "chapterID": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "formulaCards": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.FormulaCard"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Question"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TopicForCreate": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.TopicSummary": {
            "type": "object",
            "properties": {
                "chapter_id": {
                    "type": "integer"
                },
                "chapter_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "subject_id": {
                    "type": "integer"
                },
                "subject_name": {
                    "type": "string"
                }
            }
        },
        "models.Transaction": {
            "type": "object",
            "properties": {
                "amount": {
                    "description": "Amount in smallest currency unit (e.g., paise)",
                    "type": "integer"
                },
                "courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Course"
                    }
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "paymentMethod": {
                    "description": "e.g., \"UPI\", \"CARD\", \"NET_BANKING\"",
                    "type": "string"
                },
                "paymentReference": {
                    "description": "External payment gateway reference",
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "student": {
                    "$ref": "#/definitions/models.Student"
                },
                "studentID": {
                    "type": "integer"
                },
                "transactionDate": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TransactionForCreate": {
            "type": "object",
            "required": [
                "course_ids",
                "payment_method"
            ],
            "properties": {
                "course_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "payment_method": {
                    "type": "string"
                }
            }
        },
        "models.TransactionHistory": {
            "type": "object",
            "properties": {
                "total_amount": {
                    "type": "integer"
                },
                "transactions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.TransactionSummary"
                    }
                }
            }
        },
        "models.TransactionStatusUpdate": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "payment_reference": {
                    "type": "string"
                },
                "status": {
                    "type": "string",
                    "enum": [
                        "PENDING",
                        "COMPLETED",
                        "FAILED",
                        "CANCELLED"
                    ]
                }
            }
        },
        "models.TransactionSummary": {
            "type": "object",
            "properties": {
                "amount": {
                    "type": "integer"
                },
                "courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CourseWithPurchased"
                    }
                },
                "id": {
                    "type": "integer"
                },
                "payment_method": {
                    "type": "string"
                },
                "payment_reference": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "transaction_date": {
                    "type": "string"
                }
            }
        },
        "models.UpdatePassword": {
            "type": "object",
            "properties": {
                "new_password": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "properties": {
                "contactAddress": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "email": {
                    "type": "string"
                },
                "emailVerified": {
                    "type": "boolean"
                },
                "fullName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "passwordHash": {
                    "type": "string"
                },
                "phoneNumber": {
                    "type": "string"
                },
                "phoneVerified": {
                    "type": "boolean"
                },
                "role": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.UserForGet": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "phone_number": {
                    "type": "string"
                }
            }
        },
        "models.VerifyCodeRequest": {
            "type": "object",
            "required": [
                "code",
                "phone_number"
            ],
            "properties": {
                "code": {
                    "type": "string"
                },
                "phone_number": {
                    "type": "string"
                }
            }
        },
        "models.VerifyCodeResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "verified": {
                    "type": "boolean"
                }
            }
        },
        "models.Video": {
            "type": "object",
            "properties": {
                "chapterID": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "videoUrl": {
                    "type": "string"
                },
                "viewCount": {
                    "type": "integer"
                }
            }
        },
        "models.VideoForCreate": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "videoUrl": {
                    "type": "string"
                }
            }
        },
        "models.VideoForGet": {
            "type": "object",
            "properties": {
                "chapter_id": {
                    "type": "integer"
                },
                "display_name": {
                    "type": "string"
                },
                "is_completed": {
                    "type": "boolean"
                },
                "last_watched_at": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "progress_percent": {
                    "type": "number"
                },
                "progress_seconds": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "video_duration": {
                    "type": "integer"
                },
                "video_url": {
                    "type": "string"
                },
                "view_count": {
                    "type": "integer"
                }
            }
        },
        "models.VideoProgressForUpdate": {
            "type": "object",
            "required": [
                "progress_percent",
                "progress_seconds",
                "video_duration",
                "video_id"
            ],
            "properties": {
                "is_completed": {
                    "type": "boolean"
                },
                "progress_percent": {
                    "type": "number",
                    "maximum": 100,
                    "minimum": 0
                },
                "progress_seconds": {
                    "type": "integer",
                    "minimum": 0
                },
                "video_duration": {
                    "type": "integer",
                    "minimum": 1
                },
                "video_id": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "************:443",
	BasePath:         "/api/",
	Schemes:          []string{},
	Title:            "ZIA Academy App",
	Description:      "Backend server for ZIA Academy.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
